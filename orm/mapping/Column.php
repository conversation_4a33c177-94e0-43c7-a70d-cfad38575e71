<?php
/**
 * Created by lj.
 * User: lj
 * Date: 2021/6/29
 * Time: 3:45 下午
 */
namespace orm\mapping;


/**
 * @Annotation
 * @Target({"PROPERTY","ANNOTATION"})
 */
final class Column
{
    /**
     * @var string
     */
    public $name;

    /**
     * @var mixed
     */
    public $type = 'string';

    /**
     * @var integer
     */
    public $length;

    /**
     * The precision for a decimal (exact numeric) column (Applies only for decimal column).
     *
     * @var integer
     */
    public $precision = 0;

    /**
     * The scale for a decimal (exact numeric) column (Applies only for decimal column).
     *
     * @var integer
     */
    public $scale = 0;

    /**
     * @var boolean
     */
    public $unique = false;

    /**
     * @var boolean
     */
    public $nullable = false;

    /**
     * @var array
     */
    public $options = [];

    /**
     * @var string
     */
    public $columnDefinition;
}

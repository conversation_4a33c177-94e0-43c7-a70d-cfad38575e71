<?php

/**
 * Created by lj.
 * User: lj
 * Date: 2021/6/24
 * Time: 2:03 下午
 */

namespace orm;

use ReflectionClass;

class BeanTool
{
    /** 绑定数据到Bean
     * @param array $data 保存的key名与类属性名一致，且属性类型最好为mixed，特殊属性需要额外方法才能转换
     */
    public static function bind(&$bean, array $data)
    {
        $beanClass = new ReflectionClass($bean);
        $attrs = $beanClass->getProperties();
        foreach ($attrs as $item) {
            $name = $item->name;
            if (isset($data[$name])) {
                $bean->$name = $data[$name];
            }
        }
        return $bean;
    }

    public static function unbind($bean)
    {
    }
}

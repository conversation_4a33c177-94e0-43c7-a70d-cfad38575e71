<?php
/**
 * Created by lj.
 * User: lj
 * Date: 2021/6/29
 * Time: 5:35 下午
 */

namespace orm;


use think\facade\Db;

class SqlTool
{
    //如何唯一

    /**
     * 1、查询某一条数据[指定ID，条件]
     * 2、查询批次数据
     * 3、删除一条数据[指定ID，条件]
     * 4、删除多条数据
     * 5、添加一条数据
     * 6、添加多条数据
     * 7、更新一条数据
     * 8、更新多条数据
     *
     * 排序，分页，聚合，分组，事务
     *
     * 自定义查询，连接查询
     *  使用方式
     *
     *  EntityManger::create($bean)->xxxx()->select();
     *  EntityManger::create($bean)->xxxx()->find();
     *  EntityManger::create($bean)->xxxx()->count();
     *  EntityManger::create($bean)->xxx();
     *  EntityManger::findResult($bean)
     *  EntityManger::selectResult($bean)
     *
     *  Db::name('')->xxxx()->select()
     *
    */

    /**

    public function findById($id){
        $info = Db::table($this->metaInfo->tableName)->where('id', $id)->find();
        //转换成bean
        $instance = $this->classInfo->newInstance();
        foreach ($this->metaInfo->colArray as $col){
            if($col instanceof ColMetaInfo){
                $name = $col->name;
                $instance->$name = $info[$col->colName];
            }
        }
        return $instance;
    }



    public function find($map){
        $newMap = [];
        foreach ($map as $key=>$value){
            foreach ($this->metaInfo->colArray as $col){
                if($col instanceof ColMetaInfo){
                    $name = $col->name;
                    if($name === $key){
                        $newMap[$col->colName] = $value;
                        break;
                    }
                }
            }
        }

        $info = Db::table($this->metaInfo->tableName)->where($newMap)->find();
        //转换成bean
        $instance = $this->classInfo->newInstance();
        foreach ($this->metaInfo->colArray as $col){
            if($col instanceof ColMetaInfo){
                $name = $col->name;
                $instance->$name = $info[$col->colName];
            }
        }
        return $instance;
    }
    public function removeById($id){
        Db::table($this->metaInfo->tableName)->where('id', $id)->delete();
    }

    public function remove($map){
        $newMap = [];
        foreach ($map as $key=>$value){
            foreach ($this->metaInfo->colArray as $col){
                if($col instanceof ColMetaInfo){
                    $name = $col->name;
                    if($name === $key){
                        $newMap[$col->colName] = $value;
                        break;
                    }
                }
            }
        }
        Db::table($this->metaInfo->tableName)->where($newMap)->delete();
    }

    public function add($bean){
        $data = [];
        foreach ($this->metaInfo->colArray as $col){
            if($col instanceof ColMetaInfo){
                if($col->isPK && $col->isAuto){
                    continue;
                }
                $name = $col->name;
                $data[$col->colName] = $bean->$name;
            }
        }
        Db::table($this->metaInfo->tableName)->insert($data);
    }

    public function update($bean){
        $map = [];
        $data = [];
        foreach ($this->metaInfo->colArray as $col){
            if($col instanceof ColMetaInfo){
                $name = $col->name;
                if($col->isPK ){
                    $map[$col->colName] = $bean->$name;
                }else{
                    $data[$col->colName] = $bean->$name;
                }
            }
        }
        Db::table($this->metaInfo->tableName)->insert($data);
    }
     */


    /**
     * 辅助类
     */
    public static function getAddModel(TableMetaInfo $tableMetaInfo, $bean){
        $data = [];
        foreach ($tableMetaInfo->colArray as $col){
            if($col instanceof ColMetaInfo){
                if($col->isPK && $col->isAuto){
                    continue;
                }
                $name = $col->name;
                if(isset($bean->$name)){
                    $data[$col->colName] = $bean->$name;
                }
                //$data[$col->colName] = $bean->$name;
            }
        }
        return $data;
    }

    public static function getUpdateModel(TableMetaInfo $tableMetaInfo, $bean){
        $data = [];
        foreach ($tableMetaInfo->colArray as $col){
            if($col instanceof ColMetaInfo){
                if($col->isPK){
                    continue;
                }
                $name = $col->name;
                if(isset($bean->$name)){
                    $data[$col->colName] = $bean->$name;
                }
                //$data[$col->colName] = $bean->$name;
            }
        }
        return $data;
    }

    public static function getModel(TableMetaInfo $tableMetaInfo, $bean){
        $data = [];
        foreach ($tableMetaInfo->colArray as $col){
            if($col instanceof ColMetaInfo){
                $name = $col->name;
                if(isset($bean->$name)){
                    $data[$col->colName] = $bean->$name;
                }
                //$data[$col->colName] = $bean->$name;
            }
        }
        return $data;
    }

    public static function getBean(TableMetaInfo $tableMetaInfo, $data){
        $instance = $tableMetaInfo->classRef->newInstance();
        if(empty($data)){
            if ($instance instanceof BaseBean){
                $instance->reset();
            }
            return $instance;
        }

        foreach ($tableMetaInfo->colArray as $col){
            if($col instanceof ColMetaInfo){
                $name = $col->name;
                if(isset($data[$col->colName])){
                    $instance->$name = $data[$col->colName];
                }
            }
        }
        return $instance;
    }

}